import $dd from '@ali/dingtalk-jsapi/dd';
import $quit from '@ali/dingtalk-jsapi/api/biz/navigation/quit';
import $close from '@ali/dingtalk-jsapi/api/biz/navigation/close';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import $setTitle from '@ali/dingtalk-jsapi/api/biz/navigation/setTitle';

export const isPc = $dd?.env?.platform === 'pc' || $dd?.env?.platform === 'notInDingTalk';

export const isMobile = $dd?.env?.platform === 'ios' || $dd?.env?.platform === 'android';

// Real mobile device detection based on User-Agent and screen size
export const isMobileDevice = () => {
  // Check User-Agent for mobile keywords
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];
  const isMobileUA = mobileKeywords.some((keyword: string) => userAgent.includes(keyword));

  // Return true if either condition is met
  return isMobileUA;
};

// Check if running in DingTalk environment
export const isDingTalk = () => {
  // Method 1: Check DingTalk JSAPI platform
  const { platform } = $dd?.env || {};
  if (platform && platform !== 'notInDingTalk') {
    return true;
  }

  // Method 2: Check User-Agent
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.includes('dingtalk')) {
    return true;
  }

  // Method 3: Check global DingTalk objects
  if (window.dingtalk || window.dd || window.DingTalkPC) {
    return true;
  }

  return false;
};

// 关闭当前页面或者侧边栏
export const closePanel = () => {
  if (isPc) {
    // PC 端使用
    $quit({
      success() {},
      fail() {},
    });
  } else {
    // Mobile 端使用
    $close({
      success() {},
      fail() {},
    });
  }
};

// 打开链接
export const openLink = (url: string, enableShare = false) => {
  if (!isDingTalk()) {
    window.open(url, '_blank');
    return;
  }

  if (isPc) {
    $openLink({ url: `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(url)}&pc_slide=true`, enableShare });
  } else {
    $openLink({ url: `https://applink.dingtalk.com/page/link?url=${encodeURIComponent(url)}&target=panel`, enableShare });
  }
};

/**
 * Set page title for both DingTalk and browser environments
 * <AUTHOR>
 * @param {string} title - The title to set
 *
 * @example
 * setPageTitle('My Page Title');
 */
export const setPageTitle = (title: string): void => {
  if (isDingTalk()) {
    $setTitle({ title });
  } else {
    document.title = title;
  }
};

/**
 * Configure DingTalk JS-API for image upload
 * <AUTHOR>
 *
 * @example
 * configDingTalkImageUpload();
 */
export const configDingTalkImageUpload = (): Promise<void> => {
  return new Promise((resolve) => {
    if (!isDingTalk()) {
      // In non-DingTalk environment, resolve immediately for development
      resolve();
      return;
    }

    // In DingTalk environment, assume JS-API is available
    // The actual configuration should be done by the DingTalk container
    resolve();
  });
};

// 跳转到浏览器
export const getJumpBrowserUrl = (url: string) => {
  return `dingtalk://dingtalkclient/action/jump?url=${encodeURIComponent(url)}`;
};

// 打开双端链接
export const openDualLink = (url: string) => {
  return `dingtalk://dingtalkclient/action/open_platform_link?pcLink=${encodeURIComponent(`dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(url)}&popup_wnd=true&height=800&width=1200`)}&mobileLink=${encodeURIComponent(url)}`;
};
