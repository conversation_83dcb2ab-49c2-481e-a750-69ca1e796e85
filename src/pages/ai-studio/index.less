.ai-studio-page {
  min-height: 100vh;
  background:
    radial-gradient(ellipse 600px 500px at top right,
      transparent 0%,
      transparent 30%,
      rgba(20, 20, 20, 0.1) 35%,
      rgba(20, 20, 20, 0.3) 45%,
      rgba(20, 20, 20, 0.5) 55%,
      rgba(20, 20, 20, 0.7) 65%,
      rgba(20, 20, 20, 0.85) 75%,
      #141414 85%
    ),
    linear-gradient(180deg, #6B1538 41%, #2F1624 100%);
  backdrop-filter: blur(210px);
  position: relative;
  color: #FFFFFF;
  position: relative;
  overflow-x: hidden;

  // 添加模糊过渡效果的伪元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 70%;
    height: 60%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.15) 0%,
      rgba(107, 21, 56, 0.1) 20%,
      rgba(47, 22, 36, 0.08) 40%,
      rgba(47, 22, 36, 0.05) 60%,
      transparent 80%
    );
    filter: blur(50px);
    pointer-events: none;
    z-index: 1;
  }

  // 添加第二层更柔和的模糊效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80%;
    height: 70%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.08) 0%,
      rgba(47, 22, 36, 0.05) 30%,
      rgba(20, 20, 20, 0.03) 50%,
      transparent 70%
    );
    filter: blur(80px);
    pointer-events: none;
    z-index: 0;
  }

  .container {
    padding: 24px 16px;
    padding-top: calc(24px + env(safe-area-inset-top));
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
  }

  // 主要内容区域
  .main-content {
    text-align: center;
    margin-bottom: 40px;
    // AI 图标容器
    .main-logo {
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }

    .page-title {
      font-size: 24px;
      font-weight: 500;
      line-height: 34px;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 60px;
      text-align: center;
    }
  }

  // 功能卡片容器
  .feature-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .feature-card {
      background: #222224;
      border-radius: 24px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: darken(#222224, 5%);
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      &:active {
        background: darken(#222224, 10%);
        transform: translateY(0);
      }

      .card-content {
        display: flex;
        align-items: flex-start;
        flex-direction: column;

        .card-icon {
          width: 40px;
          height: 40px;
          font-size: 40px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 8px;
        }
        .card-title {
          font-size: 18px;
          font-weight: 500;
          line-height: 24px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 8px;

          .arrow-icon {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .card-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.5);
          line-height: 1.4;
        }
      }
    }
  }
}
